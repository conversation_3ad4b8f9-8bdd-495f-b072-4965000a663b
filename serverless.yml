# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: aiomics
service: ${self:custom.package.name}

plugins:
  - serverless-iam-roles-per-function
  - serverless-tag-cloud-watch-logs

custom:
  package: ${file(./package.json)}
  git: ${file(./infra/gitDetails.js)}
  cloudWatchLogsTags:
    project: ${self:service}
    stage: ${self:provider.stage}
    logGroup: ${self:service}-${self:provider.stage}
  serverless-iam-roles-per-function:
    defaultInherit: true
  stackPolicy: ${file(./infra/stackPolicy.env.yaml):${self:provider.stage}}
  logLevel:
    dev: debug
    preview: debug
    prod: info
  patientDataBucket: io.aiomics.${self:provider.region}.${self:provider.stage}.patient-data
  patientDataKmsKeyArn: ${ssm:/${self:provider.stage}/patient-data/kms/patient-data-kms-key-arn, null}

provider:
  name: aws
  profile: aiomics-${self:provider.stage}
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'eu-central-1'}
  memorySize: 1024
  timeout: 20
  versionFunctions: false # we don't use aliases against lambda versions
  logRetentionInDays: 14
  # TODO: mask sensitive data such as PII
  #       see https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/mask-sensitive-log-data-start.html
  # logDataProtectionPolicy:
  #  Name: data-protection-policy
  deploymentBucket:
    name: io.aiomics.${self:provider.region}.deploys
    serverSideEncryption: AES256
  stackPolicy: ${self:custom.stackPolicy}
  environment:
    # https://cloudash.dev/blog/aws-lambda-default-environment-variables
    SERVICE: ${self:service}
    STAGE: ${self:provider.stage}
    GIT_BRANCH: ${self:custom.git.branch}
    GIT_COMMIT_SHA: ${self:custom.git.commitSha}
    LOG_LEVEL: ${self:custom.logLevel.${opt:stage, 'dev'}}
  iamRoleStatements: [ ]
  ecr:
    scanOnPush: true
    images:
      aiomics-agents-api:
        path: ./src
        file: Dockerfile
        platform: linux/arm64 # AWS Graviton2 processor (ARM - better pricing & performance)
        buildArgs:
          # Used for labeling the Docker image
          VERSION: ${self:custom.package.version}
          GIT_SHA: ${self:custom.git.commitSha}
          DESCRIPTION: ${self:custom.package.description}
          # Python version configuration
          PYTHON_IMAGE_VERSION: 3.12.9-slim-bookworm
          PYTHON_VERSION: 3.12
          UV_VERSION: 0.6.13
          AWS_LWA_VERSION: 0.9.0
  tags:
    project: ${self:service}
    stage: ${self:provider.stage}

functions:
  agentsAPI:
    image: aiomics-agents-api # `handler` and `runtime` options not supported when using `image`
    architecture: arm64 # AWS Graviton2 processor (ARM - better pricing & performance)
    name: ${self:service}-${self:provider.stage}-AgentsAPI
    description: Agents API (Docker) - Python FastAPI (ASGI via uvicorn + AWS Lambda Adapter)
    timeout: 60 # consider using provisioned concurrency (cold-starts timeout on 20s threshold)
    environment:
      # AWS Lambda Adapter (custom runtime written in Rust, with support for HTTP response streaming via Lambda URL)
      # https://github.com/awslabs/aws-lambda-web-adapter/tree/main?tab=readme-ov-file#configurations
      AWS_LWA_ENABLE_COMPRESSION: true
      AWS_LWA_INVOKE_MODE: RESPONSE_STREAM
      AWS_LWA_READINESS_CHECK_PORT: 8080
      AWS_LWA_READINESS_CHECK_PATH: /health

      # API configuration via SSM secrets
      SSM_PATH_PREMIUM_USERS: /${self:provider.stage}/agents/premium-users
      SSM_PATH_AUTH_SECRET: /${self:provider.stage}/agents/auth-api-key
      SSM_PATH_WEAVIATE_URL: /${self:provider.stage}/agents/weaviate-url
      SSM_PATH_WEAVIATE_API_KEY: /${self:provider.stage}/agents/weaviate-api-key
      SSM_PATH_LANGFUSE_HOST: /${self:provider.stage}/agents/langfuse-host
      SSM_PATH_LANGFUSE_PUBLIC_KEY: /${self:provider.stage}/agents/langfuse-public-key
      SSM_PATH_LANGFUSE_SECRET_KEY: /${self:provider.stage}/agents/langfuse-secret-key
      SSM_PATH_OPENAI_API_KEY: /${self:provider.stage}/agents/openai-api-key
      SSM_PATH_AZURE_OPENAI_ENDPOINT: /${self:provider.stage}/agents/azure-openai-endpoint
      SSM_PATH_AZURE_OPENAI_API_KEY: /${self:provider.stage}/agents/azure-openai-api-key
      SSM_PATH_AZURE_OPENAI_API_VERSION: /${self:provider.stage}/agents/azure-openai-api-version
      SSM_PATH_AZURE_OPENAI_DEPLOYMENT_NAME: /${self:provider.stage}/agents/azure-openai-deployment-name
      SSM_PATH_GEMINI_API_KEY: /${self:provider.stage}/agents/gemini-api-key
      SSM_PATH_LLAMA_CLOUD_API_KEY: /${self:provider.stage}/agents/llama-cloud-api-key

      # Additional, non-secret, API configuration
      # REDIS_URL: todo_for_storing_rate_limits
      PATIENT_DATA_BUCKET: ${self:custom.patientDataBucket}

    iamRoleStatements:
      - Sid: AccessToSSMParameters
        Effect: Allow
        Action:
          - ssm:GetParameter*
        Resource:
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/premium-users"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/auth-api-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/openai-api-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/weaviate-url"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/weaviate-api-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/langfuse-host"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/langfuse-public-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/langfuse-secret-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/azure-openai-endpoint"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/azure-openai-api-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/azure-openai-api-version"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/azure-openai-deployment-name"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/gemini-api-key"}
          - {"Fn::Sub": "arn:aws:ssm:${self:provider.region}:${AWS::AccountId}:parameter/${self:provider.stage}/agents/llama-cloud-api-key"}

      - Sid: AccessToS3DocumentsForOCR
        Effect: Allow
        Action:
          - s3:ListBucket
          - s3:GetObject
        Resource:
          - !Sub arn:aws:s3:::${self:custom.patientDataBucket}
          - !Sub arn:aws:s3:::${self:custom.patientDataBucket}/*

      - Sid: AccessToKMSForS3Decryption
        Effect: Allow
        Action:
          - kms:Decrypt
        Resource:
          - ${self:custom.patientDataKmsKeyArn}

    # Lambda URL is the only option that allows streaming (chunked encoding)
    # AWS HTTP API Gateway & CloudFront buffer the responses
    url:
      # https://aws.amazon.com/blogs/compute/introducing-aws-lambda-response-streaming/
      invokeMode: RESPONSE_STREAM
      # cors: true
