### Node ###
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Serverless directories
.serverless
; *.env.json
; *.env.yaml
; *.env.yml
*.event.json
stack.yml
.requirements.zip   # serverless-python-requirements

# JetBrains IDEs
.idea

temp
.env