{"name": "agents", "version": "1.0.0", "description": "Python Agents API (FastAPI)", "main": "index.js", "scripts": {"api:dev": "cd src && uv run -m app.local_dev", "api:info": "sls info --stage preview", "api:invoke": "sls invoke --stage preview -f agentsAPI", "api:invoke:local": "pnpm invoke:local --stage preview -f agentsAPI", "api:logs": "sls logs --stage preview -f agentsAPI --tail", "clean": "sls requirements clean && sls requirements cleanCache && rm -rf node_modules && rm -rf .venv && rm -rf .serverless", "deploy:preview": "sls deploy --stage preview --region eu-central-1", "deploy:prod": "sls deploy --stage prod --region eu-central-1", "deploy:function": "sls deploy -f", "invoke": "sls invoke --stage preview", "invoke:local": "sls invoke local --stage preview", "offline:start": "sls offline start", "package:preview": "sls package --stage preview --region eu-central-1", "package:prod": "sls package --stage prod --region eu-central-1", "remove:preview": "sls remove --stage preview --region eu-central-1", "remove:prod": "sls remove --stage prod --region eu-central-1", "serverless": "serverless", "sls": "serverless", "run:redis": "docker run -d -p 6379:6379 redis"}, "engines": {"node": ">=22 <23"}, "private": true, "keywords": [], "author": "Stefan <PERSON>", "license": "ISC", "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531", "devDependencies": {"serverless": "^4.6.2", "serverless-iam-roles-per-function": "^3.2.0", "serverless-tag-cloud-watch-logs": "^1.0.0"}}