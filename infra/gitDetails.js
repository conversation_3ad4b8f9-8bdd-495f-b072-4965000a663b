const child_process = require('child_process');

/**
 * Execute a shell command in a child process & wrap it in a promise which returns the STDOUT contents.
 * @param command
 * @return {Promise<any>}
 */
function cmd(command) {
    return new Promise((resolve, reject) => {
        child_process.exec(command, function (err, stdout) {
            return err ? reject(err) : resolve(stdout.toString().trim());
        });
    });
}

module.exports = async () => {
    const commitSha = await cmd('git rev-parse HEAD');
    const branch = await cmd('git rev-parse --abbrev-ref HEAD');

    return { commitSha, branch };
};
