dev:
  # Anything goes
  - Effect: Allow
    Principal: '*'
    Action: Update:*
    Resource: '*'

preview:
  # Prevent delete & replace of DynamoDB tables
  - Effect: Allow
    Principal: '*'
    Action: Update:*
    Resource: '*'
  - Effect: Deny
    Principal: '*'
    Action:
      - Update:Replace
      - Update:Delete
    Resource: '*'
    Condition:
      StringEquals:
        ResourceType:
          - AWS::DynamoDB::Table

prod:
  # Prevent delete & replace of DynamoDB tables
  - Effect: Allow
    Principal: '*'
    Action: Update:*
    Resource: '*'
  - Effect: Deny
    Principal: '*'
    Action:
      - Update:Replace
      - Update:Delete
    Resource: '*'
    Condition:
      StringEquals:
        ResourceType:
          - AWS::DynamoDB::Table
