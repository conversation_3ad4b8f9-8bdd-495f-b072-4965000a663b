import os
from typing import Any, Dict, Optional

import boto3
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from google import genai
from google.genai import types

from llama_index.core import Settings, VectorStoreIndex
from llama_index.core.postprocessor import MetadataReplacementPostProcessor
from llama_index.core.prompts import PromptTemplate
from llama_index.core.tools import QueryEngineTool
from llama_index.core.workflow import (
    Context,
    Event,
    StartEvent,
    StopEvent,
    Workflow,
    step,
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.weaviate import WeaviateVectorStore

# from llama_index.llms.gemini import Gemini
# from llama_index.multi_modal_llms.gemini import GeminiMultiModal
# from llama_index.readers.google import GoogleDriveReader

from llama_index.llms.azure_openai import AzureOpenAI

from app.api.middlewares.auth import verify_api_key
from app.utils.log import get_logger
from app.utils.rag import instantiate_and_connect
from app.utils.ssm import get_parameter
from app.utils.trace import get_callback_handler


# Initialize module logger
logger = get_logger(__name__)
INDEX_NAME = "Wellster"
Settings.llm = AzureOpenAI(
    engine=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_DEPLOYMENT_NAME")),
    model="gpt-4o",
    api_key=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_API_KEY")),
    azure_endpoint=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_ENDPOINT")),
    api_version=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_API_VERSION")),
)
Settings.embed_model = OpenAIEmbedding(model="text-embedding-3-large")


class QueryPlanItemResult(Event):
    """The result of a query plan item"""

    query: str
    result: str


class QueryPlanItem(Event):
    """A single step in an execution plan for a RAG system."""

    name: str = Field(description="The name of the tool to use.")
    query: str = Field(description="A natural language search query for a RAG system.")


class ExecutedPlanEvent(Event):
    """The result of a query plan"""

    result: str


class ChatRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None


class IngestRequest(BaseModel):
    s3_key: str


class ChatResponse(BaseModel):
    response: str
    metadata: Optional[Dict[str, Any]] = None


class IngestResponse(BaseModel):
    message: str


class QueryPlan(BaseModel):
    """A plan for a RAG system. After running the plan, we should have either
    enough information to answer the user's original query,
    or enough information to form a new query plan."""

    items: list[QueryPlanItem] = Field(
        description="A list of the QueryPlanItem objects in the plan."
    )


class CareyPlanningWorkflow(Workflow):
    """Main workflow for Carey chatbot"""

    llm = Settings.llm

    planning_prompt = PromptTemplate(
        "Think step by step. Given an initial query, as well as information about the indexes you can query, return a plan for a RAG system.\n"
        "The plan should be a list of QueryPlanItem objects, where each object contains a query.\n"
        "The result of executing an entire plan should provide a result that is a substantial answer to the initial query, "
        "or enough information to form a new query plan.\n"
        "Sources you can query: {context}\n"
        "Initial query: {query}\n"
        "Plan:"
    )
    decision_prompt = PromptTemplate(
        "Given the following information, return a final response that satisfies the original query, or return 'PLAN' if you need to continue planning.\n"
        "Original query: {query}\n"
        "Current results: {results}\n"
    )

    @step
    async def planning_step(
        self, ctx: Context, ev: StartEvent | ExecutedPlanEvent
    ) -> QueryPlanItem | StopEvent:
        if isinstance(ev, StartEvent):
            # Initially, we need to plan
            request = ev.get("request")
            query = request.message
            tools = ev.get("tools")

            await ctx.set("tools", {t.metadata.name: t for t in tools})
            await ctx.set("original_query", request.message)
            await ctx.set("request_context", request.context)

            context_str = "\n".join(
                [
                    f"{i + 1}. {tool.metadata.name}: {tool.metadata.description}"
                    for i, tool in enumerate(tools)
                ]
            )
            await ctx.set("context", context_str)

            query_plan = await self.llm.astructured_predict(
                QueryPlan,
                self.planning_prompt,
                context=context_str,
                query=query,
            )

            ctx.write_event_to_stream(Event(msg=f"Planning step: {query_plan}"))

            num_items = len(query_plan.items)
            await ctx.set("num_items", num_items)
            for item in query_plan.items:
                ctx.send_event(item)
        else:
            # If we've already gone through planning and executing, we need to decide
            # if we should continue planning or if we can stop and return a result.
            query = await ctx.get("original_query")
            current_results_str = ev.result

            decision = await self.llm.apredict(
                self.decision_prompt,
                query=query,
                results=current_results_str,
            )

            # Simple string matching to see if we need to keep planning or if we can stop.
            if "PLAN" in decision:
                context_str = await ctx.get("context")
                query_plan = await self.llm.astructured_predict(
                    QueryPlan,
                    self.planning_prompt,
                    context=context_str,
                    query=query,
                )

                ctx.write_event_to_stream(Event(msg=f"Re-Planning step: {query_plan}"))

                num_items = len(query_plan.items)
                await ctx.set("num_items", num_items)
                for item in query_plan.items:
                    ctx.send_event(item)
            else:
                return StopEvent(result=decision)

    @step(num_workers=4)
    async def execute_item(
        self, ctx: Context, ev: QueryPlanItem
    ) -> QueryPlanItemResult:
        tools = await ctx.get("tools")
        tool = tools[ev.name]

        ctx.write_event_to_stream(
            Event(msg=f"Querying tool {tool.metadata.name} with query: {ev.query}")
        )

        result = await tool.acall(ev.query)

        ctx.write_event_to_stream(
            Event(msg=f"Tool {tool.metadata.name} returned: {result}")
        )

        return QueryPlanItemResult(query=ev.query, result=str(result))

    @step
    async def aggregate_results(
        self, ctx: Context, ev: QueryPlanItemResult
    ) -> ExecutedPlanEvent:
        # We need to collect the results of the query plan items to aggregate them.
        num_items = await ctx.get("num_items")
        results = ctx.collect_events(ev, [QueryPlanItemResult] * num_items)

        # collect_events returns None if not all events were found
        # return and wait for the remaining events to come in.
        if results is None:
            return

        aggregated_result = "\n------\n".join(
            [
                f"{i + 1}. {result.query}: {result.result}"
                for i, result in enumerate(results)
            ]
        )
        return ExecutedPlanEvent(result=aggregated_result)


# Create an APIRouter instance for modular routes
router = APIRouter()


@router.post(
    "/chat",
    dependencies=[
        Depends(verify_api_key),
        # Depends(rate_limit)
    ],
    response_model=ChatResponse,
)
async def chat_endpoint(request: ChatRequest):
    try:
        logger.info(f"Received chat request: {request.message}")

        # Initialize & Connect to Weaviate
        logger.info("Initialize & Connect to Weaviate")
        weaviate_client = await instantiate_and_connect(
            is_local=os.environ.get("IS_LOCAL", "").lower() == "true"
        )

        vector_store = WeaviateVectorStore(
            weaviate_client=weaviate_client, index_name=INDEX_NAME
        )
        index = VectorStoreIndex.from_vector_store(vector_store=vector_store)
        # The target key defaults to `window` to match the node_parser's default
        postproc = MetadataReplacementPostProcessor(target_metadata_key="window")
        # TODO: maybe add a rerank

        # The QueryEngine class is equipped with the generator
        # and facilitates the retrieval and generation steps
        query_engine = index.as_query_engine(
            llm=Settings.llm,
            similarity_top_k=10,
            vector_store_query_mode="hybrid",
            alpha=0.5,
            node_postprocessors=[postproc],
        )
        query_engine_tools = [
            QueryEngineTool.from_defaults(
                query_engine,
                name="weight_loss_information",
                description="Information about the weight loss management, therapy, and medications",
            )
        ]
        logger.info("Run LlamaIndex Workflow")
        workflow = CareyPlanningWorkflow(timeout=120.0, verbose=True)
        handler = workflow.run(request=request, tools=query_engine_tools)
        # stream the events as they come in
        async for event in handler.stream_events():
            if hasattr(event, "msg"):
                logger.info(
                    event.msg
                )  # TODO: replace with the function to return stream to calling function

        # get the final result
        result = await handler
        logger.info("Chat request processed successfully")
        langfuse_callback_handler = get_callback_handler()
        if langfuse_callback_handler:
            langfuse_callback_handler.flush()
        else:
            logger.warning("Langfuse callback handler not available")
        await weaviate_client.close()
        return ChatResponse(response=result)

    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/ocr",
    dependencies=[
        Depends(verify_api_key),
        # Depends(rate_limit)
    ],
    response_model=IngestResponse,
)
async def document_ocr(request: IngestRequest):
    client = genai.Client(
        # This is mandatory in AWS Lambda, otherwise you will get an error:
        #
        #   ValueError: Missing key inputs argument!
        #               To use the Google AI API,provide (`api_key`) arguments.
        #               To use the Google Cloud API, provide (`vertexai`, `project` & `location`) arguments.
        #
        # Locally, it can work without it, because it picks up the key from the environment (GEMINI_API_KEY).
        # It's better to always pass it explicitly.
        # Locally, `SSM_PATH_GEMINI_API_KEY` will delegate to GEMINI_API_KEY.
        api_key=await get_parameter(os.environ.get("SSM_PATH_GEMINI_API_KEY")),
    )
    CHUNKING_PROMPT = """You are a multimodal large language model specialized in processing medical documents. Your primary tasks are to: (1) quickly classify if an uploaded document is likely medical, and (2) if so (or if uncertain but potentially medical), perform high-fidelity Optical Character Recognition (OCR) and output the transcribed text in Markdown format, meticulously preserving the original content and structure.

Follow these instructions precisely:

**1. INITIAL MEDICAL DOCUMENT VALIDATION:**

Before proceeding with full OCR, perform a quick initial assessment to determine if the uploaded document appears to be medical in nature. Look for the following clues:

* Medical terminology or healthcare-related content.
* Healthcare institution letterheads, logos, or watermarks.
* Typical medical document structures (e.g., patient information sections, vitals, lab results, medication lists, clinical notes).
* Medical symbols, codes (e.g., ICD-10, CPT), or specialized notation.

**1.1. If the document is clearly NOT medical:**

If the document is definitively non-medical (e.g., a random photograph, a personal letter unrelated to health, a business advertisement for a non-healthcare service), respond *only* with the following exact text:

The uploaded document does not appear to be a medical document. This tool is designed specifically for medical documentation. Please upload a relevant medical document such as a lab report, medical record, prescription, or clinical notes.

**1.2. If the document is uncertain but possibly medical:**

If the document contains health-related information but doesn't clearly fit the criteria of a standard medical document (e.g., health insurance forms, wellness reports, fitness data), proceed with the OCR and Markdown transcription as described below. However, include the following note immediately after the `<BEGIN OF MARKDOWN OUTPUT>` marker:

Note: This document appears to contain health-related information but may not be a standard medical document. The extraction has been performed as requested.

**1.3. If the document is clearly medical:**

Proceed directly to the OCR and Markdown transcription as detailed in the following sections.

**2. OCR AND MARKDOWN TRANSCRIPTION GUIDELINES:**

Your primary goal is to extract all text content from the provided medical document image or PDF and convert it to Markdown format while maintaining the highest possible fidelity to the original document's content, structure, and formatting.

* **Precise Transcription:** Produce a precise transcription of *all* visible text in the original language(s). Do not translate, summarize, or paraphrase any content.
* **Preserve Original Content:** Maintain all spelling, abbreviations, numerical values, symbols, and any typographical quirks present in the document. Do not correct errors or attempt to interpret meaning.
* **Markdown Structure:** Use Markdown syntax to accurately reflect the document's layout and structure:
    * **Headings:** Use `#`, `##`, `###`, etc., to represent headings according to their visual hierarchy in the document.
    * **Lists:** Use `*` for all bulleted lists and `1.`, `2.`, etc., for numbered lists.
    * **Tables:** Use standard Markdown table syntax (`| Column 1 | Column 2 |`, `|---|---|`).
    * **Inline Elements:** Preserve bold, italic, and underlined text if visually apparent in the document.
    * **Horizontal Rules:** Use `---` to indicate significant visual breaks between sections in the document.
* **Image and Non-Textual Elements:** For images, logos, stamps, seals, graphs, charts, letterheads, and watermarks, use the following placeholder format:
    * `![Brief description of the image/element](image_placeholder)`
    * Specifically for stamps/seals and letterheads/logos, also include any visible text within brackets: `[stamp/seal: visible text]` or `[letterhead/logo: visible text]`.
    * For graphs and charts, use: `![Graph: brief description of what the graph shows]`.
* **Page Breaks:** For page breaks, use `[Page Break]` followed by an optional page number like `[Page X]` if relevant for clarity.

**3. HANDLING ILLEGIBLE, UNCERTAIN, OR SPECIAL TEXT:**

* **Partially Legible Text:** If a word or phrase is partially legible, transcribe what you can and enclose your best guess in square brackets with a question mark: `medcne [medicine?]`.
* **Completely Illegible Text:** If text is entirely unreadable, use `[illegible]` or `[unreadable]`.
* **Blurry or Obscured Text:** For blurry or obscured regions (e.g., covered by stamps or other markings), note the issue and provide your best guess in brackets: `[blurry text: best guess]` or `[obscured text: likely [guess]]`.
* **Handwritten Sections:** If you encounter handwritten text, attempt to transcribe it if legible. If it is not legible, use `[handwritten: illegible]`. If parts are readable, transcribe those and mark the rest as illegible: `[handwritten: Patient Name: John [illegible] Date: ...]`.
* **Signatures:** Indicate the presence of a signature with `[signature]`.

**4. SPECIAL FORMATTING INSTRUCTIONS:**

* **Multi-Column Layouts:** If the document has a multi-column layout, transcribe the content in a logical reading order (typically left-to-right, top-to-bottom within each column). Clearly mark the breaks between columns using `[Column Break]`.
* **Forms:** For documents that appear to be forms with labels and fillable fields, maintain the relationship between labels and their corresponding fields. For checkboxes, use `[x]` if checked and `[ ]` if unchecked.
* **Merged Cells and Nested Tables:** Do your best to represent merged cells and nested tables using standard Markdown table syntax. If the complexity makes this difficult, you can add a note like `[merged cells]` or `[nested table]` to indicate their presence.
* **Watermarks:** Note the presence of any watermarks with `[watermark: text if visible]`.

**5. STRUCTURAL RELATIONSHIP PRESERVATION (Advanced):**

While standard Markdown aims to capture structure, for complex documents, pay attention to:

* **Spatial Relationships:** Use Markdown elements (headings, lists, tables, blockquotes) to reflect the visual arrangement of content. If necessary to indicate precise positioning that Markdown cannot fully represent, you may use a brief descriptive note.
* **Label-Field Associations:** Ensure that labels and their corresponding data fields in forms remain clearly associated in the Markdown output.
* **Visual Flow:** For elements connected by lines or arrows (like in diagrams or flowcharts), try to represent this using text-based equivalents (e.g., `[Element A] --> [Element B]`).

**6. FINAL OUTPUT FORMAT:**

Begin your response with the following clear marker:

<BEGIN OF MARKDOWN OUTPUT>
Follow this marker immediately with the Markdown transcription of the document. Conclude your response with the following marker:

<END OF MARKDOWN OUTPUT>
Do not add any additional text, interpretations, disclaimers, or notes outside of the specified markers, except for the "Note" for uncertain medical documents which should appear inside the markers. Your output should be a direct and faithful representation of the document's content and structure in Markdown. If you are unsure about how to represent something, prioritize accuracy and preservation of the original over making assumptions or simplifications."""

    try:
        bucket_name = os.environ.get("PATIENT_DATA_BUCKET")
        file_key = request.s3_key

        # AWS S3 configuration
        s3_client = boto3.client(
            "s3",
            region_name=os.environ.get("AWS_REGION")
        )

        logger.info(f"Downloading file {file_key} from bucket {bucket_name}")
        
        # Download the file from S3
        try:
            response = s3_client.get_object(Bucket=bucket_name, Key=file_key)
            file_content = response["Body"].read()  # Read binary content without decoding
        except Exception as e:
            logger.error(f"Error downloading file from S3: {str(e)}")
            raise HTTPException(status_code=500, detail="Error downloading file from S3")

        logger.info("File downloaded successfully, processing with Gemini")
        
        # Process the file with Gemini
        response = client.models.generate_content(
            model="gemini-2.0-flash",
            contents=[
                types.Part.from_bytes(
                    data=file_content,
                    mime_type="application/pdf",
                ),
                CHUNKING_PROMPT,
            ],
        )
        
        # Extract the text from the response
        processed_text = response.candidates[0].content.parts[0].text
        
        # Return the complete response
        return IngestResponse(message=processed_text)

    except Exception as e:
        logger.error(f"Error in document ingestion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in document ingestion: {str(e)}")
