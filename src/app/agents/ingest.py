import os
import boto3
import mimetypes 
# import io # Removed unused import
import tempfile 
import uuid 
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Any, Dict 
from fastapi import Request 
from app.utils.log import get_logger
from app.api.middlewares.auth import verify_api_key
from app.utils.ssm import get_parameter
import asyncio
from llama_cloud_services import LlamaParse
from llama_cloud_services.types import JobStatus, JobResponse 
from llama_index.core.schema import Document as LlamaIndexDocument 
from llama_index.core.schema import ImageDocument as LlamaIndexImageDocument 

router = APIRouter()
logger = get_logger(__name__)

SYSTEM_PROMPT_APPEND = """**Handwritten text**  
        - If legible: transcribe inline.  
        - If partly legible: `[handwritten guess 0.6, upper-right corner: mild anaemia?]`.  
        - If illegible: `[handwritten, next to “Diagnosis”: illegible]`.  
        Confidence is a value 0-1; location helps reviewers.  
        For signatures write `[signature]`."""
        
class ParsedImageInfo(BaseModel):
    s3_key: str
    original_filename: Optional[str] = None
    content_type: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None 

class LlamaParseOutput(BaseModel):
    markdown_content: str
    extracted_text_s3_key: str 
    images: List[ParsedImageInfo] = []

class LlamaParseIngestResponse(BaseModel):
    original_s3_key: str
    status: str 
    message: Optional[str] = None 
    parse_output: Optional[LlamaParseOutput] = None

class IngestRequest(BaseModel):
    s3_bucket_name: str
    s3_key: str

@router.post("/ocr", response_model=LlamaParseIngestResponse, dependencies=[Depends(verify_api_key)])
async def document_ocr(request: IngestRequest):

    if not request.s3_bucket_name:
        logger.error("S3_BUCKET_NAME environment variable is not set.")
        raise HTTPException(status_code=500, detail="Server configuration error: S3 bucket name missing.")
    
    bucket_name = request.s3_bucket_name
    file_key = request.s3_key
    base_filename = os.path.splitext(os.path.basename(file_key))[0]

    s3_client = boto3.client("s3", region_name=await get_parameter(os.environ.get("SSM_PATH_AWS_REGION")))

    try:
        
        logger.info(f"Getting the object s3://{bucket_name}/{file_key}")
        response = s3_client.get_object(Bucket=bucket_name, Key=file_key)
        logger.info(f"Successfully retrieved {file_key}")
        file_bytes = response['Body'].read()

        extra_info = {"file_name": base_filename}
        parser = LlamaParse(
            api_key=await get_parameter(os.environ.get("SSM_PATH_LLAMA_CLOUD_API_KEY")),
            result_type="markdown", 
            verbose=True,
            parse_mode="parse_document_with_agent",
            preserve_layout_alignment_across_pages=True,
            annotate_links=True,
            system_prompt_append=SYSTEM_PROMPT_APPEND,
            premium_mode=True, 
        )

        logger.info(f"Starting LlamaParse for {file_key}")
        results = await parser.aparse(file_bytes, extra_info=extra_info)
        

        markdown_docs: List[LlamaIndexDocument] = results.get_markdown_documents()
        text_docs: List[LlamaIndexDocument] = results.get_text_documents()
        plain_text_content = "\n\n".join([doc.text for doc in text_docs])
        
        extracted_text_s3_key = f"parsed_text/{base_filename}_{uuid.uuid4()}.txt"
        s3_client.put_object(
            Bucket=bucket_name,
            Key=extracted_text_s3_key,
            Body=plain_text_content.encode('utf-8'),
            ContentType='text/plain; charset=utf-8'
        )
        logger.info(f"Stored extracted plain text to s3://{bucket_name}/{extracted_text_s3_key}")

        parsed_images_info: List[ParsedImageInfo] = []
        image_docs: List[LlamaIndexImageDocument] = results.get_image_documents(include_object_images=True, include_screenshot_images=False)
        
        for i, img_doc in enumerate(image_docs):
            img_bytes = img_doc.image
            if not img_bytes:
                logger.warning(f"Image document {i} for {file_key} (job {job_id}) has no image bytes. Skipping.")
                continue
            
            img_original_filename = img_doc.metadata.get("file_name", f"image_{i}")
            img_ext = os.path.splitext(img_original_filename)[1] or ".png" 
            img_content_type = mimetypes.guess_type(img_original_filename)[0] or "image/png"

            image_s3_key = f"parsed_images/{base_filename}_img_{uuid.uuid4()}{img_ext}"
            s3_client.put_object(
                Bucket=bucket_name,
                Key=image_s3_key,
                Body=img_bytes,
                ContentType=img_content_type
            )
            logger.info(f"Stored extracted image {i} to s3://{bucket_name}/{image_s3_key}")
            parsed_images_info.append(ParsedImageInfo(
                s3_key=image_s3_key,
                original_filename=img_original_filename,
                content_type=img_content_type,
                metadata=img_doc.metadata
            ))

        parse_output_data = LlamaParseOutput(
            markdown_content=markdown_docs,
            extracted_text_s3_key=extracted_text_s3_key,
            images=parsed_images_info
        )
        return LlamaParseIngestResponse(
            original_s3_key=file_key,
            status="success",
            message="Document parsed successfully.",
            parse_output=parse_output_data
        )

    except HTTPException as http_exc: 
        raise http_exc
    except Exception as e:
        logger.error(f"Error during LlamaParse ingestion for {file_key}: {e}", exc_info=True)
        return LlamaParseIngestResponse(
            original_s3_key=file_key,
            status="failed",
            message=f"An unexpected error occurred: {type(e).__name__} - {str(e)}"
        )
    finally:
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"Cleaned up temporary file: {temp_file_path}")
            except Exception as e_clean:
                logger.error(f"Failed to clean up temporary file {temp_file_path}: {e_clean}")