import os
import boto3
import mimetypes 
import io
from fastapi import APIRouter, Depends, HTTPException
from llama_index.core.workflow.retry_policy import ConstantDelayRetryPolicy
from app.utils.ssm import get_parameter
from google import genai
from google.genai import types
from llama_index.core.workflow import Workflow, step, Context, Event, StartEvent, StopEvent

from pydantic import BaseModel
from fastapi import Request 
from app.utils.log import get_logger
from app.api.middlewares.auth import verify_api_key
import asyncio

router = APIRouter()
logger = get_logger(__name__)

OCR_PROMPT = """You are a multimodal large-language model specialised in processing medical documents.  
Your workflow has TWO stages:

1.  **Document-type classification**  
    ──────────────────────────────  
    Immediately output EXACTLY ONE of the following decision tokens, and nothing else:  
    <DOC_TYPE: MEDICAL> - confident the document is medical.  
    <DOC_TYPE: NONMEDICAL> - confident the document is not medical.  

    • After **<DOC_TYPE: NONMEDICAL>** output, STOP.  
    • After **<DOC_TYPE: MEDICAL>** output, proceed immediately to stage 2 (OCR).

    *Clues for classification*  
      - Medical terminology, ICD-10/CPT codes, lab units, Rx abbreviations.  
      - Healthcare logos, letterheads, watermarks.  
      - Structures such as patient details, vitals, lab tables, prescriptions.  
      - Medical symbols (stethoscope, caduceus, red cross).

2.  **High-fidelity OCR and Markdown transcription**  
    ───────────────────────────────────────────────  
    Transcribe **all visible text exactly as printed or handwritten**.  
    Do **not** translate, paraphrase, correct spelling, merge units, or add commentary.

    • **Begin** with the marker, on its own line and in ALL-CAPS:  
      <BEGIN OF MARKDOWN OUTPUT>  

    • Use Markdown to preserve structure:  
        - Headings (`#`, `##`, …) by visual hierarchy.  
        - Lists: `*` for bullets, `1. 2. 3.` for numbered lists.  
        - Tables: `| col | col |` and `|---|---|`.  
          *Sparse tables:* even if the original has no gridlines, represent each cell separated by `|`.  
          *Merged / nested tables:* if Markdown cannot represent them, add `[merged cells]` or `[nested table]` notes.  
        - Bold/italic only when unmistakably present.  
        - Horizontal rules `---` for strong section breaks.  

    • **Images & non-text elements** (logos, seals, watermarks, graphs):  
        `![Brief description](image_placeholder)`  
        Include any visible text in brackets, e.g. `[stamp: “PAID”]` or `[watermark: “CONFIDENTIAL”]`.  

    • **Handwritten text**  
        - If legible: transcribe inline.  
        - If partly legible: `[handwritten guess 0.6, upper-right corner: mild anaemia?]`.  
        - If illegible: `[handwritten, next to “Diagnosis”: illegible]`.  
        Confidence is a value 0-1; location helps reviewers.  
        For signatures write `[signature]`.  

    • **Illegible / obscured / blurry** text  
        `[illegible]`, `[blurry text: best guess]`, `[obscured text: likely creatinine]`.  

    • **Units and numbers**  
        Reproduce units **exactly** and keep original decimal separators (`7,2 mmol/L` stays `7,2 mmol/L`).  

    • **Multi-column layouts**  
        Transcribe left-to-right, top-to-bottom.  Mark shifts with `[Column Break]`.  

    • **Forms & check-boxes**  
        Keep label-field pairing; `[x]` for checked, `[ ]` for unchecked.  

    • **Page breaks**  
        `[Page Break] [Page 2]` where relevant.  

    • **Mixed languages**  
        Retain each segment in its original language; do **not** translate.  

    • **Spatial flow / diagrams**  
        Use text equivalents such as `[Box A] --> [Box B]` to show arrows or flowlines.  

    • **No model notes**  
        Omit any internal reasoning or commentary outside the specified markers.  

    • **End** with the marker, on its own line and in ALL-CAPS:  
      <END OF MARKDOWN OUTPUT>  

    Output absolutely nothing after this end marker.
"""


class IngestResponse(BaseModel):
    message: str

class IngestRequest(BaseModel):
    s3_key: str

class OCRStartEvent(StartEvent):
    gemini_client: genai.Client
    document: bytes

class OcrResultEvent(Event):
    """Event carrying OCR text result (only if classified as MEDICAL)."""
    text: str

class DocumentOCRWorkflow(Workflow):
    """Workflow for OCR and verification using multimodal LLMs."""
    
    @step(retry_policy=ConstantDelayRetryPolicy(delay=5, maximum_attempts=3))   #TODO: might want to add context caching for the model to save on retries
    async def ocr_step(self, ctx: Context, ev: OCRStartEvent) -> OcrResultEvent | StopEvent:
        """Performs OCR, classifies doc type, extracts text if MEDICAL."""
        if hasattr(ev, "gemini_client"):
            await ctx.set("gemini_client", ev.gemini_client)
            gemini_client = ev.gemini_client
        
        if hasattr(ev, "document"):
            await ctx.set("document", ev.document)
            document = ev.document

        try:
            response = gemini_client.models.generate_content(
                model="gemini-2.5-pro",
                contents=[document, OCR_PROMPT])
            raw_text = response.text
            if "<DOC_TYPE: MEDICAL>" in raw_text:
                logger.info("Document classified as MEDICAL.")
                try:
                    start_marker = "<BEGIN OF MARKDOWN OUTPUT>"
                    end_marker = "<END OF MARKDOWN OUTPUT>"
                    
                    if start_marker not in raw_text or end_marker not in raw_text:
                        logger.error("OCR output did not contain expected markers despite MEDICAL classification.")
                        raise HTTPException(status_code=500, detail="OCR model output is missing required markers.")
                        
                    start_index = raw_text.index(start_marker) + len(start_marker)
                    end_index = raw_text.rindex(end_marker) 
                    ocr_text = raw_text[start_index:end_index].strip()
                    
                    if not ocr_text:
                         logger.warning("MEDICAL classification but extracted OCR text is empty.")
                         raise HTTPException(status_code=500, detail="OCR model failed to provide text content after MEDICAL classification.")
                    
                    logger.info(f"Successfully extracted {len(ocr_text)} characters of OCR text.")
                    return OcrResultEvent(text=ocr_text)
                except ValueError as ve:
                    logger.error(f"Could not find OCR start/end markers despite MEDICAL classification: {ve}")
                    raise HTTPException(status_code=500, detail="OCR model failed to provide valid markdown output structure.")
            
            elif "<DOC_TYPE: NONMEDICAL>" in raw_text:
                return StopEvent(result="The uploaded document does not appear to be a medical document. This tool is designed specifically for medical documentation. Please upload a relevant medical document such as a lab report, medical record, prescription, or clinical notes")
            
            else:
                logger.error("No valid DOC_TYPE token found in OCR model response.")
                raise HTTPException(status_code=500, detail="OCR model failed to classify document type as instructed.")

        except types.BlockedPromptException as bpe:
            logger.error(f"Gemini API request blocked during OCR: {bpe}")
            raise HTTPException(status_code=400, detail=f"Content policy violation or blocked prompt during OCR: {bpe}")
        except Exception as e:
            logger.error(f"Error during OCR step: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error during OCR processing: {type(e).__name__}")

    @step
    async def verify_step(self, ctx: Context, ev: OcrResultEvent) -> StopEvent:
        """Verifies the extracted OCR text against the original document using Gemini."""
        
        
        gemini_client: genai.Client = await ctx.get("gemini_client")
        document: types.Document = await ctx.get("document")

        ocr_text = ev.text

        try:
            logger.info(f"Calling verification model ({self.verify_model}) with {len(ocr_text)} chars of OCR text.")
            
            verification_prompt = f"""You are an expert medical document processor. Below is an OCR transcription of the provided document. 
Review the transcription against the original document image. 
Correct *only transcription errors* (e.g., typos, misread characters, formatting issues like incorrect line breaks or spacing). 
Do **not** add information, interpret content, or change medical terminology unless it's clearly a transcription mistake. 
Preserve the original structure and Markdown formatting used in the initial transcription. 
Provide the final, corrected transcription below, starting immediately after the 'Corrected Transcription:' line.
Do not include the 'Corrected Transcription:' line in your answer, just start directly with the corrected content.

OCR Transcription:
---
{ocr_text}
---
Corrected Transcription:"""

            

            response = await gemini_client.generate_content_async(
                model="gemini-2.5-pro",
                contents=[document, verification_prompt],
                safety_settings=[
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}
                ]
            )

            if not response.candidates or not response.candidates[0].content or not response.candidates[0].content.parts or not response.text:
                logger.error("Verification model returned an empty or invalid response structure.")
                raise HTTPException(status_code=500, detail="Verification model returned an invalid response.")
            
            verified_text = response.text
            
            # Extract the corrected text from the response if needed
            if "Corrected Transcription:" in verified_text:
                verified_text = verified_text.split("Corrected Transcription:", 1)[1].strip()
                
            logger.info("Verification step completed successfully.")
            return StopEvent(result=verified_text.strip()) 

        except types.BlockedPromptException as bpe:
             logger.error(f"Gemini API verification request blocked: {bpe}")
             raise HTTPException(status_code=400, detail=f"Verification content policy violation or blocked prompt: {bpe}")
        except Exception as e:
            logger.error(f"Error during verification step: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error during verification: {type(e).__name__}")

@router.post("/ocr", response_model=IngestResponse, dependencies=[Depends(verify_api_key)])
async def document_ocr(request: IngestRequest):
    """Ingests a user-uploaded document, performs OCR and verification via workflow."""
    
    aws_region = os.environ.get("AWS_REGION")    

    # Validate the extension for supported image/document types
    file_key = request.s3_key
    file_extension = os.path.splitext(file_key)[1].lower()
    supported_extensions = ['.jpg', '.jpeg', '.png', '.pdf', '.tiff', '.bmp', '.webp', '.gif']
    
    if not any(file_key.lower().endswith(ext) for ext in supported_extensions):
        logger.warning(f"Unsupported file type: {file_extension}. Supported types: {', '.join(supported_extensions)}")
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type: {file_extension}. Please upload one of these formats: {', '.join(supported_extensions)}"
        )

    # Download the file from S3
    s3_client = boto3.client("s3", region_name=aws_region)
    try:
        logger.info(f"Downloading s3://{bucket_name}/{file_key}")
        obj = s3_client.get_object(Bucket=bucket_name, Key=file_key)
        file_bytes: bytes = obj["Body"].read()
        doc_io = io.BytesIO(file_bytes) 
        logger.info(f"Successfully downloaded {len(file_bytes)} bytes ({len(file_bytes) / (1024 * 1024):.2f} MB).")
        
        
            
    except Exception as exc:
        logger.error(f"S3 download failed for s3://{bucket_name}/{file_key}: {exc}", exc_info=True)
        status_code = 404 if 'NoSuchKey' in str(exc) else 500
        detail = "File not found in S3" if status_code == 404 else "Error downloading file from S3"
        raise HTTPException(status_code=status_code, detail=detail)

    gemini_client = genai.Client(api_key=await get_parameter("SSM_PATH_GEMINI_API_KEY"))
    document = gemini_client.files.upload(file=doc_io)
    ocr_start_event = OCRStartEvent(document=document, gemini_client=gemini_client)
    logger.info(f"Starting DocumentOCRWorkflow for {file_key}... ")
    workflow = DocumentOCRWorkflow() 
    result = await workflow.run(start_event=ocr_start_event)
    logger.info(f"Workflow completed successfully for {file_key}. Result length: {len(result) if result else 0}")
    return IngestResponse(message=result)
            
