import os

import weaviate
from weaviate import WeaviateAsyncClient
from weaviate.classes.init import Auth

from app.utils.log import get_logger
from app.utils.ssm import get_parameter

logger = get_logger(__name__)


async def instantiate_and_connect(is_local=False) -> WeaviateAsyncClient:
    """
    Connect to Weaviate and return a connected WeaviateAsyncClient

    Args:
        is_local: Whether to connect to local Weaviate in Docker, or Weaviate Cloud.

    Returns:
        The parameter value.
    """
    if is_local:
        logger.info("Connect to local Weaviate")
        client = weaviate.use_async_with_local()
    else:
        logger.info("Connect to Weaviate Cloud")
        client = weaviate.use_async_with_weaviate_cloud(
            cluster_url=await get_parameter(os.environ.get("SSM_PATH_WEAVIATE_URL")),
            auth_credentials=Auth.api_key(
                await get_parameter(os.environ.get("SSM_PATH_WEAVIATE_API_KEY"))
            ),
        )
    await client.connect()
    return client
