import os

from langfuse.llama_index import LlamaIndexCallbackHandler
from llama_index.core import Settings
from llama_index.core.callbacks import CallbackManager

from app.utils.log import get_logger
from app.utils.ssm import get_parameter

logger = get_logger(__name__)

_langfuse_callback_handler = None


async def initialize_langfuse():
    """
    Helper function to initialize the Langfuse handler
    """
    global _langfuse_callback_handler
    logger.info("LlamaIndex: initialize Langfuse")
    _langfuse_callback_handler = LlamaIndexCallbackHandler(
        public_key=await get_parameter(os.environ.get("SSM_PATH_LANGFUSE_PUBLIC_KEY")),
        secret_key=await get_parameter(os.environ.get("SSM_PATH_LANGFUSE_SECRET_KEY")),
        host=await get_parameter(os.environ.get("SSM_PATH_LANGFUSE_HOST")),
    )
    Settings.callback_manager = CallbackManager([_langfuse_callback_handler])


def get_callback_handler():
    """Return the global Langfuse callback handler.
    Raises an exception if the handler has not been initialised.
    """
    global _langfuse_callback_handler
    if _langfuse_callback_handler is None:
        raise Exception(
            "Langfuse callback handler has not been initialised. Please call initialize_langfuse first."
        )
    return _langfuse_callback_handler
