import logging
import logging.config
import os
from typing import Optional

# Get log level from environment variable, default to "DEBUG"
LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG").upper()

LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "%(asctime)s | %(levelname)s | %(name)s | %(module)s:%(lineno)d | %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "simple": {"format": "%(levelname)s | %(name)s | %(message)s"},
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "detailed",
            "level": LOG_LEVEL,
            "stream": "ext://sys.stdout",
        }
    },
    "loggers": {
        # Specific logger configurations
        "botocore": {"level": "WARNING", "propagate": True},
        "boto3": {"level": "WARNING", "propagate": True},
        "urllib3": {"level": "WARNING", "propagate": True},
        "src": {"level": LOG_LEVEL, "handlers": ["console"], "propagate": False},
    },
    "root": {"level": LOG_LEVEL, "handlers": ["console"]},
}

# Apply the configuration
logging.config.dictConfig(LOGGING_CONFIG)


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    If no name is provided, returns a logger named after the calling module.

    Args:
        name: Optional name for the logger. If None, uses the calling module's name.

    Returns:
        logging.Logger: Configured logger instance
    """
    if name is None:
        # Get the caller's module name
        import inspect

        frame = inspect.currentframe()
        if frame is not None:
            frame = frame.f_back
            if frame is not None:
                name = frame.f_globals["__name__"]

    return logging.getLogger(name or "root")
