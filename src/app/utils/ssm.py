import os
import time

from aiobotocore.session import get_session
from botocore.exceptions import ClientError

from app.utils.log import get_logger

logger = get_logger(__name__)

# Cache and timestamp to store retrieved parameters
_ssm_cache = {}
_last_refresh_time = 0  # Stores last refresh timestamp
REFRESH_INTERVAL = 300  # 5 minutes

# AWS Region
AWS_REGION = os.getenv("AWS_REGION", "eu-central-1")


async def load_secrets(
    parameter_names, with_decryption=True, region=AWS_REGION, use_env=False
):
    """
    Fetch multiple parameters in bulk from AWS Parameter Store and store them in the cache asynchronously.

    Args:
        parameter_names: List of parameter names to fetch.
        with_decryption: Whether to decrypt SecureString parameters.
        region: AWS Region.
        use_env: Whether to use environment variables, instead of fetching from SSM.

    Returns:
        Dictionary containing parameter names and values.
    """
    global _ssm_cache, _last_refresh_time

    if use_env:
        logger.debug("Fetched parameters from local env. Store in cache.")
        _ssm_cache.update({name: os.environ.get(name) for name in parameter_names})

        # Update refresh timestamp
        _last_refresh_time = time.time()
        return _ssm_cache

    try:
        session = get_session()
        async with session.create_client("ssm", region_name=region) as ssm:
            logger.debug("Fetching parameters from AWS Parameter Store")
            response = await ssm.get_parameters(
                Names=parameter_names, WithDecryption=with_decryption
            )

            # Store retrieved parameters in cache
            logger.debug("Fetched parameters from AWS Parameter Store. Store in cache.")
            _ssm_cache.update(
                {
                    param["Name"]: param["Value"]
                    for param in response.get("Parameters", [])
                }
            )

            # Log any missing parameters
            invalid_parameters = response.get("InvalidParameters", [])
            if invalid_parameters:
                logger.warning(
                    f"Some parameters could not be retrieved: {invalid_parameters}"
                )

            # Update refresh timestamp
            _last_refresh_time = time.time()
            return _ssm_cache

    except ClientError as e:
        logger.error(f"AWS Parameter Store error: {str(e)}")
        raise Exception(f"Failed to fetch parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in load_secrets: {str(e)}")
        raise


async def get_parameter(parameter_name, with_decryption=True, region=AWS_REGION):
    """
    Fetch a parameter from AWS Parameter Store asynchronously, using cache when available.

    Args:
        parameter_name: Name of the parameter to fetch.
        with_decryption: Whether to decrypt the parameter (for SecureString parameters).
        region: AWS Region.

    Returns:
        The parameter value.

    Raises:
        Exception: If parameter cannot be fetched.
    """
    global _ssm_cache, _last_refresh_time

    # Check if cache needs refresh
    if time.time() - _last_refresh_time > REFRESH_INTERVAL:
        logger.debug("Refreshing secrets from AWS SSM...")
        await load_secrets(
            list(_ssm_cache.keys()), with_decryption=with_decryption, region=region
        )

    # Return from cache if available
    if parameter_name in _ssm_cache:
        logger.debug(f"Found parameter {parameter_name} in cache.")
        return _ssm_cache[parameter_name]

    # If not in cache, fetch it and store in cache
    try:
        logger.debug(f"Fetching parameter {parameter_name}...")
        session = get_session()
        async with session.create_client("ssm", region_name=region) as ssm:
            response = await ssm.get_parameter(
                Name=parameter_name, WithDecryption=with_decryption
            )
            _ssm_cache[parameter_name] = response["Parameter"]["Value"]
            return _ssm_cache[parameter_name]

    except ClientError as e:
        logger.error(f"AWS Parameter Store error: {str(e)}")
        raise Exception(f"Failed to fetch parameter {parameter_name}: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in get_parameter: {str(e)}")
        raise
