import os

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request

from app.utils.log import get_logger
from app.utils.ssm import get_parameter

logger = get_logger(__name__)


async def verify_api_key(request: Request):
    """
    Dependency function for API key authentication on a per-route basis.

    TODO: https://blog.lamona.tech/how-to-authenticate-api-requests-with-clerk-and-fastapi-6ac5196cace7
    """
    # Fetch API key from AWS SSM (cached)
    logger.info("Authenticating API key ...")
    API_KEY = await get_parameter(os.environ.get("SSM_PATH_AUTH_SECRET"))

    # Extract API key from headers
    api_key = request.headers.get("X-API-Key")

    if not api_key or api_key != API_KEY:
        logger.error("Invalid API key")
        raise HTTPException(status_code=403, detail="Invalid API key")

    logger.info("API key authentication successful")
    return True  # Allow request to proceed
