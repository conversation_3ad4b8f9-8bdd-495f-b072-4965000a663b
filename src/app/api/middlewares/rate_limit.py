import os

import aioredis
from fastapi import Request
from slowapi import <PERSON><PERSON>
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address
from starlette.responses import JSONResponse

from app.utils.log import get_logger
from app.utils.ssm import get_parameter

# Setup logger
logger = get_logger(__name__)

# Initialize Redis connection
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost")
redis = aioredis.from_url(REDIS_URL, decode_responses=True)

# Create a rate limiter instance using Redis storage
limiter = Limiter(
    key_func=lambda request: get_api_key_or_ip(request), storage_uri=REDIS_URL
)


# Exception handler for rate limit errors
def rate_limit_exceeded_handler(request: Request, exc: RateLimitExceeded):
    """
    Handles rate limit violations by returning a 429 response.
    """
    logger.warning(f"Rate limit exceeded for {request.client.host}")
    return JSONResponse(
        status_code=429,
        content={"detail": "Too many requests, please slow down"},
    )


async def get_api_key_or_ip(request: Request):
    """
    Determines the key for rate limiting: Uses API key if available, otherwise falls back to IP.
    """
    api_key = request.headers.get("X-API-Key")
    return f"user:{api_key}" if api_key else get_remote_address(request)


async def get_rate_limit_for_user(request: Request):
    """
    Fetches the rate limit dynamically based on the user's API key.
    Uses the existing in-memory cache from `get_parameter()`.
    """
    api_key = request.headers.get("X-API-Key")

    # Fetch premium users from AWS SSM (in-memory cache already applied)
    premium_users = await get_parameter(os.getenv("SSM_PATH_PREMIUM_USERS"))
    premium_users = premium_users.split(",") if premium_users else []

    if api_key in premium_users:
        logger.info(f"Premium user {api_key} accessing the API")
        return "50/minute"  # Higher limit for premium users

    logger.info(f"Regular user {api_key or 'IP-based'} accessing the API")
    return "10/minute"  # Default rate limit for free users


async def track_api_usage(request: Request):
    """
    Tracks API usage in Redis for analytics and monitoring.
    Uses Redis pipeline for efficiency.
    """
    key = await get_api_key_or_ip(request)
    route = request.url.path
    redis_key = f"api_usage:{key}:{route}"

    async with redis.pipeline() as pipe:
        await pipe.incr(redis_key)
        await pipe.expire(redis_key, 3600)  # Auto-expire after 1 hour
        await pipe.execute()


async def rate_limit(request: Request):
    """
    Applies dynamic rate limiting and tracks API usage in Redis.
    """
    limit = await get_rate_limit_for_user(request)

    # Track API usage in Redis
    await track_api_usage(request)

    logger.info(f"Applying rate limit: {limit} for {await get_api_key_or_ip(request)}")
    return limiter.limit(limit)(request)
