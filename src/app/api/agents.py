import asyncio
import os
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from llama_index.core import Settings
from llama_index.embeddings.google_genai import GoogleGenAIEmbedding
from llama_index.llms.google_genai import GoogleGenAI

from llama_index.llms.azure_openai import AzureOpenAI

# from slowapi.errors import RateLimitExceeded
# from app.api.middlewares.rate_limit import rate_limit_exceeded_handler
from app.agents.carey import router as carey_router

# from app.agents.quinn import router as quinn_router
from app.utils.log import get_logger
from app.utils.ssm import get_parameter, load_secrets
from app.utils.trace import initialize_langfuse

logger = get_logger(__name__)

# List of parameters to fetch on startup
SSM_PARAMETERS = [
    # os.environ.get("SSM_PATH_PREMIUM_USERS"),
    os.environ.get("SSM_PATH_AUTH_SECRET"),
    os.environ.get("SSM_PATH_OPENAI_API_KEY"),
    os.environ.get("SSM_PATH_WEAVIATE_URL"),
    os.environ.get("SSM_PATH_WEAVIATE_API_KEY"),
    os.environ.get("SSM_PATH_LANGFUSE_HOST"),
    os.environ.get("SSM_PATH_LANGFUSE_PUBLIC_KEY"),
    os.environ.get("SSM_PATH_LANGFUSE_SECRET_KEY"),
    os.environ.get("SSM_PATH_GEMINI_API_KEY"),
    os.environ.get("SSM_PATH_AZURE_OPENAI_ENDPOINT"),
    os.environ.get("SSM_PATH_AZURE_OPENAI_API_KEY"),
    os.environ.get("SSM_PATH_AZURE_OPENAI_DEPLOYMENT_NAME"),
    os.environ.get("SSM_PATH_AZURE_OPENAI_API_VERSION"),
]


@asynccontextmanager
async def lifespan(__app: FastAPI):
    """
    Define logic that executes before the application starts.
    See https://fastapi.tiangolo.com/advanced/events/#lifespan
    """
    logger.info("API startup: loading secrets")
    await load_secrets(
        SSM_PARAMETERS, use_env=os.environ.get("IS_LOCAL", "").lower() == "true"
    )

    logger.info("API startup: initialize Langfuse")
    await initialize_langfuse()
    
    # Initialize the LLM and store it in app.state for dependency injection
    logger.info("API startup: initializing LLM")
    azure_openai_models = await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_MODELS"))
    __app.state.llms = {
        "gpt-4o": AzureOpenAI(
            engine=azure_openai_models["gpt-4o"]["deployment"],
            model=azure_openai_models["gpt-4o"]["model_name"],
            api_key=azure_openai_models["gpt-4o"]["api_key"],
            azure_endpoint=azure_openai_models["gpt-4o"]["endpoint"],
            api_version=azure_openai_models["gpt-4o"]["api_version"],
        ),
        "gpt-4.1": AzureOpenAI(
            engine=azure_openai_models["gpt-4.1"]["deployment"],
            model=azure_openai_models["gpt-4.1"]["model_name"],
            api_key=azure_openai_models["gpt-4.1"]["api_key"],
            azure_endpoint=azure_openai_models["gpt-4.1"]["endpoint"],
            api_version=azure_openai_models["gpt-4.1"]["api_version"],
        ),
        "gemini-2.5-pro": GoogleGenAI(
            model_name="gemini-2.5-pro",
            api_key=await get_parameter(os.environ.get("SSM_PATH_GEMINI_API_KEY")),
        ),
        # "mini": AzureOpenAI(
        #     engine=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_MINI_DEPLOYMENT_NAME")),
        #     model="gpt-4o-mini",  # Not deployed yet
        #     api_key=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_API_KEY")),
        #     azure_endpoint=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_ENDPOINT")),
        #     api_version=await get_parameter(os.environ.get("SSM_PATH_AZURE_OPENAI_API_VERSION")),
        # ),
    }
    
    # Set the default LLM for llama_index (could be the primary one)
    Settings.llm = __app.state.llms["primary"]
    
    # Initialize embedding model
    __app.state.embed_model = GoogleGenAIEmbedding(
    model_name="gemini-embedding-exp-03-07",
    api_key=await get_parameter(os.environ.get("SSM_PATH_GEMINI_API_KEY")),
)
    Settings.embed_model = __app.state.embed_model

    yield


app = FastAPI(title="Agents API", lifespan=lifespan)

# Register rate limit exception handler
# app.add_exception_handler(RateLimitExceeded, rate_limit_exceeded_handler)

# Agent routers (protected with API key auth)
app.include_router(carey_router, prefix="/api/v1/carey", tags=["carey"])
# app.include_router(quinn_router, prefix="/api/v1/quinn", tags=["quinn"])


# Public routes
@app.get("/")
def read_root():
    logger.info("Agent read: /")
    return {"Status": "Agents API up & running."}


@app.get("/health")
async def health_check():
    logger.info("Agent read: /health")
    return {"status": "healthy"}


async def generate_stream():
    for word in ["Hello", "this", "is", "a", "streaming", "response"]:
        yield word + " "
        await asyncio.sleep(0.5)  # Simulate processing time


@app.get("/stream")
async def stream():
    logger.info("Agent read: /stream")
    return StreamingResponse(generate_stream(), media_type="text/event-stream")


if __name__ == "__main__":
    # Lambda Web Adapter
    # - forwards the request context sent by API Gateway via "x-amzn-request-context"
    # - forwards the lambda context sent by Lambda via "x-amzn-context"
    uvicorn.run(app, host="0.0.0.0", port=int(os.environ.get("PORT", "8080")))
