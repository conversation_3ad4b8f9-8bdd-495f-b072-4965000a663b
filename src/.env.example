# https://cloudash.dev/blog/aws-lambda-default-environment-variables
AWS_PROFILE=aiomics-dev
AWS_REGION=eu-central-1
PATIENT_DATA_BUCKET=io.aiomics.eu-central-1.dev.patient-data
STAGE=dev
LOG_LEVEL=debug
IS_LOCAL=True

# Re-route SSM params to local env vars when running locally.
# This is so we avoid re-fetching from AWS SSM on every FastAPI hot reload.
SSM_PATH_PREMIUM_USERS=PREMIUM_USERS
SSM_PATH_AUTH_SECRET=AUTH_SECRET
SSM_PATH_OPENAI_API_KEY=OPENAI_API_KEY
SSM_PATH_WEAVIATE_URL=WEAVIATE_URL
SSM_PATH_WEAVIATE_API_KEY=WEAVIATE_API_KEY
SSM_PATH_LANGFUSE_HOST=LANGFUSE_HOST
SSM_PATH_LANGFUSE_PUBLIC_KEY=LANGFUSE_PUBLIC_KEY
SSM_PATH_LANGFUSE_SECRET_KEY=LANGFUSE_SECRET_KEY
SSM_PATH_AZURE_OPENAI_ENDPOINT=AZURE_OPENAI_ENDPOINT
SSM_PATH_AZURE_OPENAI_API_KEY=AZURE_OPENAI_API_KEY
SSM_PATH_GEMINI_API_KEY=GEMINI_API_KEY

PREMIUM_USERS=
AUTH_SECRET=my-secret-key
OPENAI_API_KEY=__add_credential__
WEAVIATE_URL=__add_credential__
WEAVIATE_API_KEY=__add_credential__
LANGFUSE_HOST=__add_credential__
LANGFUSE_PUBLIC_KEY=__add_credential__
LANGFUSE_SECRET_KEY=__add_credential__
GEMINI_API_KEY=__add_credential__
