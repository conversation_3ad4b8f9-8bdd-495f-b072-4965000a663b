# https://docs.astral.sh/uv/guides/integration/aws-lambda/#deploying-a-docker-image
#
# The core benefits of this Dockerfile structure are as follows:
#
#   - Minimal image size.
#     By using a multi-stage build, we can ensure that the final image only includes
#     the application code and dependencies. For example, the uv binary itself is not included in the final image.
#   - Maximal cache reuse.
#     By installing application dependencies separately from the application code,
#     we can ensure that the Docker layer cache is only invalidated when the dependencies change.
#
# Concretely, rebuilding the image after modifying the application source code can reuse the cached layers,
# resulting in millisecond builds.

# Define ARGs that will be used in FROM statements
# These default values are typically overridden by values from serverless.yml
# They serve as fallbacks and documentation of expected values
ARG PYTHON_IMAGE_VERSION=3.12.9-slim-bookworm
ARG PYTHON_VERSION=3.12
ARG UV_VERSION=0.6.13
ARG AWS_LWA_VERSION=0.9.0

# UV stage for dependency management
FROM ghcr.io/astral-sh/uv:${UV_VERSION} AS uv

# AWS Lambda Web Adapter stage
FROM public.ecr.aws/awsguru/aws-lambda-adapter:${AWS_LWA_VERSION} AS lambda-adapter

# Builder Stage:
# - bundle the dependencies into a directory
# - populate a single directory with all application code and dependencies
FROM public.ecr.aws/docker/library/python:${PYTHON_IMAGE_VERSION} AS builder

# Enable bytecode compilation, to improve cold-start performance.
ENV UV_COMPILE_BYTECODE=1

# Disable installer metadata, to create a deterministic layer.
ENV UV_NO_INSTALLER_METADATA=1

# Enable copy mode to support bind mount caching.
ENV UV_LINK_MODE=copy

# Set up the working directory
WORKDIR /server

# Bundle the dependencies
#
# Omit any local packages (`--no-emit-workspace`) and development dependencies (`--no-dev`).
# This ensures that the Docker layer cache is only invalidated when the `pyproject.toml` or `uv.lock`
# files change, but remains robust to changes in the application code.
RUN --mount=from=uv,source=/uv,target=/bin/uv \
    --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv export --frozen --no-emit-workspace --no-dev --no-editable -o requirements.txt && \
    uv pip install --system -r requirements.txt

# Production Stage:
# - copy the directory (bundled with dependencies) over to the final image, omitting the build tools and other unnecessary files
FROM public.ecr.aws/docker/library/python:${PYTHON_IMAGE_VERSION}

# Define ARGs in this stage where they're used
ARG VERSION
ARG GIT_SHA
ARG DESCRIPTION

# Docker-specific metadata
LABEL maintainer="Nikita Tarasov <<EMAIL>>, Stefan Tertan <<EMAIL>>"
LABEL version="${VERSION}"
LABEL description="${DESCRIPTION}"

# OCI metadata
LABEL org.opencontainers.image.source="https://github.com/aiomics/agents"
LABEL org.opencontainers.image.authors="Nikita Tarasov <<EMAIL>>, Stefan Tertan <<EMAIL>>"
LABEL org.opencontainers.image.version="${VERSION}"
LABEL org.opencontainers.image.description="${DESCRIPTION}"
LABEL org.opencontainers.image.created='$(date -u +"%Y-%m-%dT%H:%M:%SZ")'
LABEL org.opencontainers.image.revision="${GIT_SHA}"
LABEL org.opencontainers.image.licenses="Proprietary"
LABEL org.opencontainers.image.documentation="https://github.com/aiomics/agents/blob/main/README.md"
LABEL org.opencontainers.image.vendor="Aiomics"

# Set up the working directory
WORKDIR /server

# Redeclare ARG in this stage to use it
ARG PYTHON_VERSION

# Copy the installed dependencies from the builder stage
COPY --from=builder /usr/local/lib/python${PYTHON_VERSION}/site-packages /usr/local/lib/python${PYTHON_VERSION}/site-packages

# Copy only the necessary application code files
# Explicitly include only the Python modules and packages needed for runtime
COPY ./app/__init__.py /server/app/__init__.py
COPY ./app/api /server/app/api
COPY ./app/agents /server/app/agents
COPY ./app/utils /server/app/utils
# Exclude development, test files, and __pycache__ directories

# Add the AWS Lambda Adapter (custom runtime written in Rust, with support for HTTP response streaming via Lambda URL)
COPY --from=lambda-adapter /lambda-adapter /opt/extensions/lambda-adapter

# Start the FastAPI application served by uvicorn
CMD ["python", "-m", "app.api.agents"]
