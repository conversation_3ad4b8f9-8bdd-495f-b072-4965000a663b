[project]
name = "agents-api"
version = "0.1.0"
description = "Python Agents API (FastAPI)"
authors = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON> Tertan", email = "<EMAIL>" }
]
maintainers = [
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "Stefan Tertan", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "aiobotocore>=2.21.1",
    "aioredis>=2.0.1",
    "boto3>=1.37.1",
    "fastapi>=0.115.12",
    "google-genai>=1.9.0",
    "langfuse>=2.60.2",
    "llama-index>=0.12.28",
    "llama-index-agent-openai>=0.4.6",
    "llama-index-core>=0.12.28",
    "llama-index-embeddings-openai>=0.3.1",
    "llama-index-llms-azure-openai>=0.3.2",
    "llama-index-llms-gemini>=0.4.14",
    "llama-index-llms-openai>=0.3.30",
    "llama-index-multi-modal-llms-gemini>=0.5.0",
    "llama-index-utils-workflow>=0.3.1",
    "llama-index-vector-stores-weaviate>=1.3.1",
    "pydantic>=2.11.2",
    "python-dotenv>=1.1.0",
    "slowapi>=0.1.9",
    "uvicorn>=0.34.0",
    "weaviate-client>=4.12.0",
]

[dependency-groups]
dev = [
    "ruff>=0.11.4",
]
