# Agents

## Prerequisites

### The Serverless Framework

Make sure you have Node.js with pnpm installed:

```shell
# Use NVM (Node Version Manager) to install Node
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash

# Install Node v22
nvm install 22

# Make v22 the default Node version
nvm alias default v22

# Enable PNPM (package manager, alternative to `npm` and `yarn`)
corepack enable pnpm

# Upgrade PNPM to the latest version
corepack up
```

Install project dependencies:

```shell
pnpm install
```

### AWS Config

AWS only allows you to create 2 IAM Key Credentials per user. Since we're using a single AWS account,
you can create one key and reuse the credentials for all 3 envs.
I created 2 keys (one shared between `dev` & `preview`, and a separate one for `prod`).

Create/edit `~/.aws/config`:

```text
[profile aiomics-dev]
region=eu-central-1
output=json

[profile aiomics-preview]
region=eu-central-1
output=json

[profile aiomics-prod]
region=eu-central-1
output=json
```

Create/edit `~/.aws/credentials`:

```text
[aiomics-dev]
aws_access_key_id = AKIA...
aws_secret_access_key = ...

[aiomics-preview]
aws_access_key_id = AKIA...
aws_secret_access_key = ...

[aiomics-prod]
aws_access_key_id = AKIA...
aws_secret_access_key = ...
```

### Python

We use [uv](https://docs.astral.sh/uv/) for Python dependency management:

```shell
# Install uv
brew install uv

# Enable shell autocompletion for uv & uvx
echo 'eval "$(uv generate-shell-completion bash)"' >> ~/.bashrc
echo 'eval "$(uvx --generate-shell-completion bash)"' >> ~/.bashrc

uv version

# [Optional] Install Python version (globally, not in venv)
uv python install 3.14

# [Optional] You can pin the Python version used in the current directory
# - this tells uv which version to use when creating the virtual environment
# - this is persisted to `.python-version`
uv python pin 3.14

# [Optional] Create a virtual environment
# - this is automatically done when installing the dependencies (see below)
# - this will not activate the newly created venv
uv venv
```

Manage dependencies using uv:

```shell
# [Example] Install dependencies from requirements file
# uv pip sync requirements.txt

# Install dependencies
uv sync

# Manage prod dependencies
uv add weaviate-client
uv remove weaviate-client
uv lock             # create a lock file

# Manage dev dependencies
uv add --dev ruff
uv add --dev pytest

# Upgrade dependencies
uv lock --upgrade-package weaviate-client

# View dependency tree
uv tree
```

Run linter

```shell
uv run ruff check
```

## Running locally

### On Linux

> First copy `.env.example` into `.env`.

```shell
# Ensure you are in the `src` folder, where all Python-related code and configuration is stored
cd src

# NOTE:
#   - `uv run` runs the scripts in the virtual environment
#   - you don't need to activate the virtual environment beforehand (unless you want to run `mkdocs` directly)

# Run API (FastAPI via uvicorn)
uv run -m local_dev

# Call local running APIs
curl -X GET "http://localhost:8000/"
curl -X GET "http://localhost:8000/health"

curl -X POST "http://localhost:8000/api/v1/carey/chat" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: my-secret-key" \
     -d '{"message": "hello my friend!"}'

curl -X POST "http://localhost:8000/api/v1/carey/ingest" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: my-secret-key" \
     -d '{"s3_key": "patients/user_2tlOEaSpGmMXGLspaUJD7jEB42x/01JR9AFK5PZJ7S8MANT4DKXG1D.pdf"}'

# Call local running API & stream response
curl -N http://localhost:8000/stream

# Call remote APIs (preview env)
time curl -X GET "https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/" \
  -w "@timing-info.curl-format.txt"

time curl -X GET "https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/health" \
  -w "@timing-info.curl-format.txt"

time curl -X POST "https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/api/v1/carey/chat" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: my-secret-key" \
     -d '{"message": "hello my friend!"}' \
     -w "@timing-info.curl-format.txt"

time curl -X POST "https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/api/v1/carey/ingest" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: my-secret-key" \
     -d '{"s3_key": "patients/user_2tlOEaSpGmMXGLspaUJD7jEB42x/01JR9AFK5PZJ7S8MANT4DKXG1D.pdf"}' \
     -w "@timing-info.curl-format.txt"

# Call remote API & stream response
curl -N https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/stream
```

### Using The Serverless Framework

> NOTE: https://www.serverless.com/framework/docs/guides/upgrading-v4#license-changes

```shell
# Make sure you activate the Python venv first (and that you have the dependencies installed)
source .venv/bin/activate
pip install -r requirements.txt

# Invoke the function locally using The Serverless Framework
pnpm api:invoke:local
```

#### Serverless Offline Plugin

Tried https://www.serverless.com/plugins/serverless-offline-python but it fails with:
> **✖ TypeError: Os.tmpDir is not a function**

> TODO: Might work! See https://github.com/alhazmy13/serverless-offline-python/issues/18

#### Dev Mode

> **ERROR: Dev mode does not yet support the "python3.12" runtime.**
> **It currently only supports JavaScript and TypeScript on the Node.js runtime.**

> _This is not running fully locally! The Serverless Framework will actually deploy your service
> (CloudFormation stack). If the service is already deployed, then a CloudFormation stack update
> will be performed, to wrap the lambdas with some lightweight code.
> The wrapper is actually a proxy between AWS Lambda and your machine:_
> - _events are proxied to your local machine_
> - _code runs locally_
> - _response is proxied back to AWS, so the lambda can return it_

> _There is no need to set local variables in `.env`, as they are retrieved from AWS Lambda itself._

```shell
# Runs `sls dev`
pnpm api:dev
```

## Remote Invocation

```shell
# Optional (better logs as we are building the Docker image)
export SLS_DEBUG=*

# Deploy your lambda to preview
pnpm deploy:preview

# Tail the logs in the terminal
pnpm api:logs

# Call your lambda from code, curl, etc using the Lambda URL endpoint
curl -X POST "https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/api/v1/carey/chat" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: my-secret-key" \
     -d '{"message": "hello my friend!"}'

curl -N https://znsvns7jhhibcegwfwrweomhfq0widyh.lambda-url.eu-central-1.on.aws/stream

# Or invoke the function directly using The Serverless Framework (this doesn't go through API GW)
pnpm api:invoke
```

## Rate Limits

| Feature                                           | Implementation                  |
|---------------------------------------------------|---------------------------------|
| ✅ Persistent rate limiting across instances       | Redis storage for API limits    |
| ✅ Supports API-key-based and IP-based rate limits | Dynamic per-user limits         |
| ✅ Tracks API usage for analytics                  | Redis stores usage logs         |
| ✅ Prevents abuse                                  | Limits requests per minute/hour |

### How It Works

- Redis stores rate limit counters (`api_usage:{user_or_ip}:{route}`).
- Ensures rate limits persist across multiple instances (no in-memory loss).
- Auto-expires keys after 1 hour to prevent infinite growth.
- Logs API usage, making it easy to track and analyze user activity
